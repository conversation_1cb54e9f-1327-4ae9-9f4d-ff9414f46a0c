export const HTTP_STATUS = {
  OK: 200,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  REQUEST_TOO_LARGE: 413,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_ERROR: 500,
} as const;

export const CORS_ORIGINS = [
  "http://localhost:5173",
  "http://localhost:3000",
  "https://app.auth-clear.com",
  "https://www.app.auth-clear.com",
  "https://auth-clear.com",
  "https://www.auth-clear.com",
  "https://d1sjbz0lbvqcjz.cloudfront.net",
] as string[];

export const DEFAULT_FRONTEND_URL = "https://app.auth-clear.com";

export const CORS_HEADERS = {
  DEFAULT: {
    "Content-Type": "application/json",
    "Access-Control-Allow-Origin": "*",
  } as const,
  IFRAME: {
    "Content-Type": "application/json",
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
    "Access-Control-Allow-Headers": "Content-Type, Authorization, X-Requested-With, X-Frame-Options",
    "Access-Control-Allow-Credentials": "false",
    "X-Frame-Options": "ALLOWALL",
    "Content-Security-Policy":
      "frame-ancestors *; " +
      "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://test-api.payrix.com https://api.payrix.com https://pay.google.com https://apis.google.com https://www.gstatic.com; " +
      "connect-src 'self' https://test-api.payrix.com https://api.payrix.com https://pay.google.com https://apis.google.com https://www.googleapis.com; " +
      "frame-src 'self' https://pay.google.com https://test-api.payrix.com https://api.payrix.com; " +
      "img-src 'self' data: https: blob:; " +
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; " +
      "font-src 'self' https://fonts.gstatic.com;",
    "X-Content-Type-Options": "nosniff",
    "X-XSS-Protection": "1; mode=block",
    "Referrer-Policy": "strict-origin-when-cross-origin",
    "Permissions-Policy": "payment=*, geolocation=(), microphone=(), camera=()",
  } as const,
} as const;
