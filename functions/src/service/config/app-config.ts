import { parameterService } from "../ssm/parameter-service.js";

export interface PayrixConfig {
  apiUrl: string;
  privateApiKey: string;
  publicApiKey: string;
}

export interface AppConfig {
  stage: string;
  payrix: PayrixConfig;
  frontendUrl: string;
  defaultOrgId: string;
  tables: {
    paymentTokens: string;
    merchantData: string;
  };
}

export class ConfigService {
  private static instance: ConfigService;
  private config: AppConfig | null = null;

  private constructor() {}

  public static getInstance(): ConfigService {
    if (!ConfigService.instance) {
      ConfigService.instance = new ConfigService();
    }
    return ConfigService.instance;
  }

  async getConfig(): Promise<AppConfig> {
    if (this.config) {
      return this.config;
    }

    const stage = process.env.STAGE || "dev";
    const isOffline = process.env.IS_OFFLINE === "true";
    const parameterPrefix = `/auth-clear/${stage}`;

    // For local development with serverless offline, use environment variables
    if (isOffline) {
      console.log("Running in offline mode, using environment variables");
      this.config = {
        stage,
        payrix: {
          apiUrl: process.env.PAYRIX_API_URL || "",
          privateApiKey: process.env.PAYRIX_PRIVATE_API_KEY || "",
          publicApiKey: process.env.PAYRIX_PUBLIC_API_KEY || "",
        },
        frontendUrl: process.env.FRONTEND_URL || "",
        defaultOrgId: process.env.DEFAULT_ORG_ID || "",
        tables: {
          paymentTokens: process.env.PAYMENT_TOKENS_TABLE_NAME || "",
          merchantData: process.env.MERCHANT_DATA_TABLE_NAME || "",
        },
      };
      return this.config;
    }

    // Use SSM Parameter Store for deployed environments
    try {
      console.log(`Loading configuration from SSM Parameter Store for stage: ${stage}`);

      const parameterNames = [
        `${parameterPrefix}/payrix/private-api-key`,
        `${parameterPrefix}/payrix/public-api-key`,
        `${parameterPrefix}/payrix/api-url`,
        `${parameterPrefix}/frontend-url`,
        `${parameterPrefix}/default-org-id`,
      ];

      const parameters = await parameterService.getParameters(parameterNames);

      // Helper function to find parameter value by name suffix
      const getParameterValue = (suffix: string): string => {
        const param = parameters.find((p) => p.name.endsWith(suffix));
        if (!param) {
          throw new Error(`Missing required parameter ending with: ${suffix}`);
        }
        return param.value;
      };

      this.config = {
        stage,
        payrix: {
          apiUrl: getParameterValue("api-url"),
          privateApiKey: getParameterValue("private-api-key"),
          publicApiKey: getParameterValue("public-api-key"),
        },
        frontendUrl: getParameterValue("frontend-url"),
        defaultOrgId: getParameterValue("default-org-id"),
        tables: {
          paymentTokens: process.env.PAYMENT_TOKENS_TABLE_NAME || "",
          merchantData: process.env.MERCHANT_DATA_TABLE_NAME || "",
        },
      };

      console.log(`Successfully loaded configuration from SSM for stage: ${stage}`);
      return this.config;
    } catch (error) {
      console.error(`Failed to load configuration from SSM for stage ${stage}:`, error);

      if (process.env.PAYRIX_PRIVATE_API_KEY && process.env.PAYRIX_PUBLIC_API_KEY) {
        console.warn("SSM failed, falling back to environment variables");
        this.config = {
          stage,
          payrix: {
            apiUrl: process.env.PAYRIX_API_URL || "",
            privateApiKey: process.env.PAYRIX_PRIVATE_API_KEY,
            publicApiKey: process.env.PAYRIX_PUBLIC_API_KEY,
          },
          frontendUrl: process.env.FRONTEND_URL || "",
          defaultOrgId: process.env.DEFAULT_ORG_ID || "",
          tables: {
            paymentTokens: process.env.PAYMENT_TOKENS_TABLE_NAME || "",
            merchantData: process.env.MERCHANT_DATA_TABLE_NAME || "",
          },
        };
        return this.config;
      } else {
        throw new Error(`Configuration loading failed for ${stage}: ${error instanceof Error ? error.message : String(error)}`);
      }
    }
  }

  clearCache(): void {
    this.config = null;
    parameterService.clearCache();
  }
}

export const configService = ConfigService.getInstance();
