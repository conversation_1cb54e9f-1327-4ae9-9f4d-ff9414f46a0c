import { DynamoDBClient } from "@aws-sdk/client-dynamodb";
import { DynamoDBDocumentClient } from "@aws-sdk/lib-dynamodb";
import { fromIni } from "@aws-sdk/credential-provider-ini";

let dynamoClient: DynamoDBDocumentClient | null = null;

export const TABLE_NAME = process.env.PAYMENT_TOKENS_TABLE_NAME || "";

const isOffline = process.env.IS_OFFLINE === "true";
const isDev = process.env.STAGE === "dev";

export function getDynamoClient(): DynamoDBDocumentClient {
  if (!dynamoClient) {
    const clientConfig: {
      region: string;
      credentials?: ReturnType<typeof fromIni>;
    } = {
      region: process.env.AWS_REGION || "us-east-1",
    };

    if (isOffline && isDev) {
      clientConfig.credentials = fromIni({ profile: "payrix" });
    }

    const client = new DynamoDBClient(clientConfig);
    dynamoClient = DynamoDBDocumentClient.from(client, {
      marshallOptions: {
        removeUndefinedValues: true,
      },
    });
  }
  return dynamoClient;
}
