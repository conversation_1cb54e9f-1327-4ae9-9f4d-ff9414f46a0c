import axios, { AxiosInstance } from "axios";
import { PAYRIX_ERROR_MESSAGES } from "../../constants/payrix.constants.js";
import { configService } from "../config/app-config.js";

let payrixApiClient: AxiosInstance | null = null;

export async function createPayrixApiClient(): Promise<AxiosInstance> {
  if (payrixApiClient) {
    return payrixApiClient;
  }

  try {
    const config = await configService.getConfig();

    if (!config.payrix.privateApiKey) {
      throw new Error(PAYRIX_ERROR_MESSAGES.API_KEY_REQUIRED);
    }

    payrixApiClient = axios.create({
      baseURL: config.payrix.apiUrl,
      headers: {
        "Content-Type": "application/json",
        APIKEY: config.payrix.privateApiKey,
      },
    });

    return payrixApiClient;
  } catch (error) {
    throw new Error(`Failed to create Payrix API client: ${error instanceof Error ? error.message : String(error)}`);
  }
}

export async function getPayrixConfig() {
  const config = await configService.getConfig();
  return {
    PAYRIX_API_URL: config.payrix.apiUrl,
    PAYRIX_PRIVATE_API_KEY: config.payrix.privateApiKey,
    PAYRIX_PUBLIC_API_KEY: config.payrix.publicApiKey,
  };
}
