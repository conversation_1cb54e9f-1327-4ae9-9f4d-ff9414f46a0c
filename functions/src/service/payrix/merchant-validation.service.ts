import { AxiosError } from "axios";
import { logger } from "../../helpers/logger.js";
import { PayrixMerchantEntity, MerchantValidationResult } from "../../types/payrix.types.js";
import { createPayrixApiClient } from "./api-client.js";
import { isApprovedMCCCode } from "../../constants/approvedMccCodes.js";
import { HTTP_STATUS } from "../../constants/http.constants.js";
import { PAYRIX_MERCHANT_STATUS } from "../../constants/payrix.constants.js";

export async function checkMerchantExists(email: string, _ein?: string): Promise<boolean> {
  try {
    const apiClient = await createPayrixApiClient();
    const response = await apiClient.get(`/entities`);

    const merchants: PayrixMerchantEntity[] = response.data?.response?.data || [];
    const emailExists = merchants.some((merchant: PayrixMerchantEntity) => merchant.email?.toLowerCase() === email.toLowerCase());

    return emailExists;
  } catch (error) {
    const axiosError = error as AxiosError;
    logger.error("Error checking merchant existence", {
      status: axiosError.response?.status,
      statusText: axiosError.response?.statusText,
      data: axiosError.response?.data,
      message: axiosError.message,
    });

    return false;
  }
}

export async function validateMerchantById(merchantId: string): Promise<MerchantValidationResult> {
  try {
    const apiClient = await createPayrixApiClient();
    const merchantResponse = await apiClient.get(`/merchants/${merchantId}`);
    const merchantData = merchantResponse.data?.response?.data?.[0];

    if (!merchantData) {
      return { isValid: false, error: "Merchant not found" };
    }

    let entityData = null;
    if (merchantData.entity) {
      try {
        const entityResponse = await apiClient.get(`/entities/${merchantData.entity}`);
        entityData = entityResponse.data?.response?.data?.[0];
      } catch (entityError) {
        logger.warn("Failed to fetch entity data", {
          entityId: merchantData.entity,
          error: (entityError as AxiosError).message,
        });
      }
    }

    const combinedMerchant: PayrixMerchantEntity = {
      id: merchantData.id,
      status: merchantData.status,
      inactive: merchantData.inactive || 0,
      frozen: merchantData.frozen || 0,
      created: merchantData.created || "",
      modified: merchantData.modified || "",
      mcc: merchantData.mcc,
      name: entityData?.name || merchantData.dba || "",
      legal_name: entityData?.name || merchantData.dba || "",
      dba: merchantData.dba || entityData?.name || "",
      email: entityData?.email || "",
      phone: entityData?.phone || "",
      address1: entityData?.address1 || "",
      address2: entityData?.address2 || "",
      city: entityData?.city || "",
      state: entityData?.state || "",
      zip: entityData?.zip || "",
      country: entityData?.country || "",
    };

    const validationResult = validateMerchantStatus(combinedMerchant);
    if (!validationResult.isValid) {
      return validationResult;
    }

    const mccValidation = validateMerchantMCC(combinedMerchant, merchantId);
    if (!mccValidation.isValid) {
      return mccValidation;
    }

    return {
      isValid: true,
      merchant: combinedMerchant,
    };
  } catch (error) {
    const axiosError = error as AxiosError;
    logger.error("Error validating merchant by ID", {
      merchantId,
      status: axiosError.response?.status,
      statusText: axiosError.response?.statusText,
      data: axiosError.response?.data,
      message: axiosError.message,
    });

    if (axiosError.response?.status === HTTP_STATUS.NOT_FOUND) {
      return {
        isValid: false,
        error: "Merchant not found",
      };
    }

    return {
      isValid: false,
      error: `Validation failed: ${axiosError.message}`,
    };
  }
}

function validateMerchantStatus(merchant: PayrixMerchantEntity): MerchantValidationResult {
  const isActive =
    PAYRIX_MERCHANT_STATUS.ACTIVE_STATUSES.includes(merchant.status) &&
    merchant.inactive === PAYRIX_MERCHANT_STATUS.INACTIVE &&
    merchant.frozen === PAYRIX_MERCHANT_STATUS.FROZEN;
  return isActive
    ? { isValid: true }
    : {
        isValid: false,
        error: `Merchant is not active (status: ${merchant.status}, inactive: ${merchant.inactive}, frozen: ${merchant.frozen})`,
      };
}

function validateMerchantMCC(merchant: PayrixMerchantEntity, _merchantId: string): MerchantValidationResult {
  const mccCode = merchant.mcc as string;
  if (mccCode && !isApprovedMCCCode(mccCode)) {
    return {
      isValid: false,
      error: `Merchant MCC code ${merchant.mcc} is not approved for payment processing. Only specific business categories are currently supported.`,
    };
  }

  return { isValid: true };
}
