import { AxiosError } from "axios";
import { logger } from "../../helpers/logger.js";
import { createPayrixApiClient } from "./api-client.js";
import { parsePayrixResponse, parsePayrixErrorResponse } from "./response-parser.js";
import { PAYRIX_ERROR_MESSAGES } from "../../constants/payrix.constants.js";

interface NoteResponse {
  id: string;
  entity?: string;
  note?: string;
  type?: string;
}

export async function createNote(noteData: { 
  entity: string; 
  note: string; 
  type?: string; 
  login?: string 
}): Promise<{ id: string }> {
  try {
    const apiClient = await createPayrixApiClient();
    logger.info("Creating note in Payrix", {
      entity: noteData.entity,
      type: noteData.type,
      noteLength: noteData.note.length,
    });

    const response = await apiClient.post("/notes", noteData);

    logger.info("Payrix note creation response", {
      status: response.status,
      data: response.data,
    });

    const noteResponse = parsePayrixResponse<NoteResponse>(response, "note");

    if (!noteResponse.id) {
      throw new Error(PAYRIX_ERROR_MESSAGES.NOTE_MISSING_ID);
    }

    logger.info("Note created successfully", {
      noteId: noteResponse.id,
      entity: noteData.entity,
    });

    return { id: noteResponse.id };
  } catch (error) {
    logger.error("Error creating note in Payrix", { error });
    
    if (error instanceof AxiosError) {
      logger.error("Payrix API error details", {
        status: error.response?.status,
        data: error.response?.data,
        message: error.message,
      });
      
      const errorMessage = parsePayrixErrorResponse(error, "Note creation");
      throw new Error(errorMessage);
    }
    
    throw error;
  }
}