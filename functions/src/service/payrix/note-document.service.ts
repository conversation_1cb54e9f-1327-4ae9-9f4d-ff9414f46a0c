import { AxiosError } from "axios";
import FormData from "form-data";
import { logger } from "../../helpers/logger.js";
import { createPayrixApiClient } from "./api-client.js";
import { parsePayrixResponse, handlePayrixStatusError } from "./response-parser.js";
import { HTTP_STATUS } from "../../constants/http.constants.js";
import { PAYRIX_ERROR_MESSAGES } from "../../constants/payrix.constants.js";

const UPLOAD_DEFAULTS = {
  INTEGRATION: "WORLDPAY",
  DIRECTION: "upload",
  DOCUMENT_TYPE: "voidCheck",
  DEFAULT_DESCRIPTION: "Void check for bank account verification",
  DEFAULT_EXTENSION: "png",
} as const;

interface NoteDocumentResponse {
  id: string;
  note?: string;
  type?: string;
  documentType?: string;
  name?: string;
}

export async function createNoteDocument(documentData: {
  note: string;
  file: { filename: string; content: Buffer; contentType: string };
  description?: string;
}): Promise<{ id: string }> {
  try {
    const noteDocumentId = await createNoteDocumentMetadata(documentData);
    await uploadFileToNoteDocument(noteDocumentId, documentData);
    return { id: noteDocumentId };
  } catch (error) {
    handleNoteDocumentError(error as AxiosError, documentData);
    throw error;
  }
}

async function createNoteDocumentMetadata(documentData: {
  note: string;
  file: { filename: string; content: Buffer; contentType: string };
  description?: string;
}): Promise<string> {
  const fileExtension = documentData.file.filename.split(".").pop()?.toLowerCase() || UPLOAD_DEFAULTS.DEFAULT_EXTENSION;

  const noteDocumentPayload = {
    note: documentData.note,
    type: fileExtension,
    documentType: UPLOAD_DEFAULTS.DOCUMENT_TYPE,
    description: documentData.description || UPLOAD_DEFAULTS.DEFAULT_DESCRIPTION,
    name: documentData.file.filename,
  };

  logger.info("Creating note document metadata", {
    noteId: documentData.note,
    payload: noteDocumentPayload,
  });

  const apiClient = await createPayrixApiClient();
  const noteDocResponse = await apiClient.post("/noteDocuments", noteDocumentPayload);

  logger.info("Note document metadata response", {
    status: noteDocResponse.status,
    data: noteDocResponse.data,
  });

  const noteDocumentData = parsePayrixResponse<NoteDocumentResponse>(noteDocResponse, "note document");

  if (!noteDocumentData.id) {
    throw new Error(PAYRIX_ERROR_MESSAGES.NOTE_DOCUMENT_MISSING_ID);
  }

  return noteDocumentData.id;
}

async function uploadFileToNoteDocument(
  noteDocumentId: string,
  documentData: {
    note: string;
    file: { filename: string; content: Buffer; contentType: string };
    description?: string;
  }
): Promise<void> {
  const formData = new FormData();

  formData.append("file", documentData.file.content, {
    filename: documentData.file.filename,
    contentType: documentData.file.contentType,
  });

  const uploadPayload = {
    integration: UPLOAD_DEFAULTS.INTEGRATION,
    direction: UPLOAD_DEFAULTS.DIRECTION,
    name: documentData.file.filename,
    description: documentData.description || "Void Check for Bank Account Verification",
  };

  formData.append("json", JSON.stringify(uploadPayload));

  const apiClient = await createPayrixApiClient();
  await apiClient.post(`/files/noteDocuments/${noteDocumentId}`, formData, {
    headers: {
      ...formData.getHeaders(),
    },
  });
}

function handleNoteDocumentError(
  error: AxiosError,
  documentData: {
    note: string;
    file: { filename: string; content: Buffer; contentType: string };
    description?: string;
  }
): void {
  logger.error(PAYRIX_ERROR_MESSAGES.ERROR_IN_3_STEP_PROCESS, {
    error,
    noteId: documentData.note,
    fileName: documentData.file.filename,
  });

  if (error instanceof AxiosError) {
    logger.error("Payrix API error details", {
      status: error.response?.status,
      data: error.response?.data,
      message: error.message,
    });

    const status = error.response?.status;
    const payrixData = error.response?.data as Record<string, unknown> | undefined;

    if (status === HTTP_STATUS.NOT_FOUND) {
      throw new Error(PAYRIX_ERROR_MESSAGES.NOTE_NOT_FOUND);
    }
    
    if (status === HTTP_STATUS.REQUEST_TOO_LARGE) {
      throw new Error(PAYRIX_ERROR_MESSAGES.FILE_TOO_LARGE);
    }

    handlePayrixStatusError(status, payrixData, "Note document");
  }
}