export interface ValidationResult {
  isValid: boolean;
  statusCode?: number;
  error?: string;
  message?: string;
  details?: Record<string, unknown>;
}

export function sanitizeInput(input: string): string {
  if (!input || typeof input !== "string") {
    return "";
  }

  return input
    .replace(/[<>"'&]/g, "")
    .replace(/\s+/g, " ")
    .trim()
    .substring(0, 500);
}

export function validateTokenFormat(token: string): ValidationResult {
  if (!token || typeof token !== "string") {
    return {
      isValid: false,
      statusCode: 400,
      error: "Invalid token format",
      message: "Token must be a non-empty string",
    };
  }

  // Accept both 32-character Payrix payment tokens and 64-character integration tokens
  const hexPattern32 = /^[a-f0-9]{32}$/i; // Payrix payment tokens
  const hexPattern64 = /^[a-f0-9]{64}$/i; // Integration tokens

  if (!hexPattern32.test(token) && !hexPattern64.test(token)) {
    return {
      isValid: false,
      statusCode: 400,
      error: "Invalid token format",
      message: "Token must be a 32 or 64-character hexadecimal string",
    };
  }

  return { isValid: true };
}

export function validateMerchantIdFormat(merchantId: string): ValidationResult {
  if (!merchantId || typeof merchantId !== "string") {
    return {
      isValid: false,
      statusCode: 400,
      error: "Invalid merchant ID",
      message: "Merchant ID is required and must be a string",
    };
  }

  const merchantIdPattern = /^[a-zA-Z0-9_-]{1,50}$/;
  if (!merchantIdPattern.test(merchantId)) {
    return {
      isValid: false,
      statusCode: 400,
      error: "Invalid merchant ID format",
      message: "Merchant ID must be alphanumeric with optional hyphens/underscores, 1-50 characters",
    };
  }

  return { isValid: true };
}

export function validateDescription(description: string): ValidationResult {
  if (!description || typeof description !== "string") {
    return {
      isValid: false,
      statusCode: 400,
      error: "Invalid description",
      message: "Description is required and must be a string",
    };
  }

  const trimmedDescription = description.trim();
  if (trimmedDescription.length < 3) {
    return {
      isValid: false,
      statusCode: 400,
      error: "Description too short",
      message: "Description must be at least 3 characters long",
    };
  }

  if (trimmedDescription.length > 255) {
    return {
      isValid: false,
      statusCode: 400,
      error: "Description too long",
      message: "Description must not exceed 255 characters",
    };
  }

  return { isValid: true };
}

export function validateAmount(amount: number): ValidationResult {
  if (typeof amount !== "number" || isNaN(amount)) {
    return {
      isValid: false,
      statusCode: 400,
      error: "Invalid amount",
      message: "Amount must be a valid number",
    };
  }

  if (amount < 10) {
    return {
      isValid: false,
      statusCode: 400,
      error: "Amount too low",
      message: "Amount must be at least $0.10 (10 cents)",
    };
  }

  if (amount > 10000000) {
    return {
      isValid: false,
      statusCode: 400,
      error: "Amount too high",
      message: "Amount must not exceed $100,000 (10000000 cents)",
    };
  }

  return { isValid: true };
}

export function validateReturnUrl(url: string): ValidationResult {
  if (!url || typeof url !== "string") {
    return {
      isValid: false,
      statusCode: 400,
      error: "Invalid return URL",
      message: "Return URL is required",
    };
  }

  try {
    const urlObj = new URL(url);
    if (urlObj.protocol !== "https:") {
      return {
        isValid: false,
        statusCode: 400,
        error: "Invalid return URL protocol",
        message: "Return URL must use HTTPS protocol for security",
      };
    }
  } catch {
    return {
      isValid: false,
      statusCode: 400,
      error: "Invalid return URL format",
      message: "Return URL must be a valid URL",
    };
  }

  return { isValid: true };
}
