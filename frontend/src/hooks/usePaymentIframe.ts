import { useEffect, useState } from "react";
import { useSearchParams } from "react-router-dom";
import { validateIframeToken } from "../services/api";
import type { PayFieldsConfig, MerchantInfo, PaymentInfo, GooglePayConfig } from "../types/payment";
import { configureIframeBodyStyles, configureIframeViewport, formatErrorMessage } from "../utils/paymentUtils";
import { isProduction } from "../config/payrixFieldMapping";

interface UsePaymentIframeReturn {
  payFieldsConfig: PayFieldsConfig | null;
  merchantInfo: MerchantInfo | null;
  paymentInfo: PaymentInfo | null;
  error: string | null;
  loading: boolean;
}

export const usePaymentIframe = (): UsePaymentIframeReturn => {
  const [searchParams] = useSearchParams();
  const [payFieldsConfig, setPayFieldsConfig] = useState<PayFieldsConfig | null>(null);
  const [merchantInfo, setMerchantInfo] = useState<MerchantInfo | null>(null);
  const [paymentInfo, setPaymentInfo] = useState<PaymentInfo | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    // Configure iframe-specific styling
    configureIframeBodyStyles();
    configureIframeViewport();

    // Get token from URL parameters
    const tokenParam = searchParams.get("token");

    if (!tokenParam) {
      setError("Missing payment token. Please use a valid payment link.");
      setLoading(false);
      return;
    }

    // Load configuration with both payment methods enabled
    const validateTokenAndLoadConfig = async () => {
      try {
        // Load configuration with both card and Google Pay enabled
        const data = await validateIframeToken(tokenParam, "card");

        if (!data.success || !data.data) {
          throw new Error(data.message || "Invalid token or configuration");
        }

        // Ensure Google Pay is enabled in the configuration
        const googlePayConfig: GooglePayConfig = {
          enabled: true,
          merchantName: data.data.merchantInfo.name,
        };

        if (!isProduction) {
          googlePayConfig.environment = "TEST";
        }

        const configWithBothMethods = {
          ...data.data.config,
          googlePayConfig,
        };

        setPayFieldsConfig(configWithBothMethods);
        setMerchantInfo(data.data.merchantInfo);
        setPaymentInfo(data.data.paymentInfo);
        setLoading(false);

        // Notify parent window that iframe is ready
        if (window.parent !== window) {
          window.parent.postMessage(
            {
              type: "PAYMENT_IFRAME_READY",
              data: {
                merchantName: data.data.merchantInfo.name,
                amount: data.data.paymentInfo.amount,
                description: data.data.paymentInfo.description,
              },
            },
            "*"
          );
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Failed to load payment configuration";
        setError(errorMessage);
        setLoading(false);

        if (window.parent !== window) {
          window.parent.postMessage(formatErrorMessage(errorMessage, "PAYMENT_IFRAME_ERROR"), "*");
        }
      }
    };

    validateTokenAndLoadConfig();
  }, [searchParams]);

  return {
    payFieldsConfig,
    merchantInfo,
    paymentInfo,
    error,
    loading,
  };
};
