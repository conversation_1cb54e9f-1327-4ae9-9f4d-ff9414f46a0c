import { useState } from "react";
import { toast } from "sonner";
import { generateIntegrationToken } from "../../../services/api";
import { GenerateIntegrationTokenRequest, GooglePayConfig } from "../../../services/types/payment";
import { DemoConfig } from "../types";
import { DEFAULT_GOOGLE_PAY_CONFIG, isProduction } from "../../../config/payrixFieldMapping";

export const useTokenGeneration = () => {
  const [embedUrl, setEmbedUrl] = useState<string>("");
  const [loading, setLoading] = useState(false);

  const generateToken = async (config: DemoConfig) => {
    setLoading(true);
    setEmbedUrl("");

    try {
      const tokenRequest: GenerateIntegrationTokenRequest = {
        merchantId: config.merchantId,
        description: config.description,
        amount: config.amount,
        returnUrl: config.returnUrl,
        expiresIn: 60,
      };

      if (config.enableGooglePay) {
        tokenRequest.enableDigitalWallets = true;
        const googlePayConfig: GooglePayConfig = {
          enabled: true,
          merchantName: config.googlePayMerchantName || "Auth-Clear",
          allowedCardNetworks: [...DEFAULT_GOOGLE_PAY_CONFIG.allowedCardNetworks],
          allowedCardAuthMethods: [...DEFAULT_GOOGLE_PAY_CONFIG.allowedCardAuthMethods],
          billingAddressRequired: DEFAULT_GOOGLE_PAY_CONFIG.billingAddressRequired,
          phoneNumberRequired: DEFAULT_GOOGLE_PAY_CONFIG.phoneNumberRequired,
        };

        if (!isProduction) {
          googlePayConfig.environment = "TEST";
        }

        tokenRequest.googlePayConfig = googlePayConfig;
      }

      const response = await generateIntegrationToken(tokenRequest);

      if (response.success && response.data) {
        setEmbedUrl(response.data.embedUrl);
        toast.success("Integration token generated successfully!");
        return true;
      } else {
        throw new Error("Failed to generate token");
      }
    } catch {
      toast.error("Failed to generate integration token");
      return false;
    } finally {
      setLoading(false);
    }
  };

  return {
    embedUrl,
    loading,
    generateToken,
  };
};
