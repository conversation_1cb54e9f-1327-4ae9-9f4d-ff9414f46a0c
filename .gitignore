node_modules
.serverless
# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history
setup-parameters.js

# Output of 'npm pack'
*.tgz

# dotenv environment variable files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.production

# Serverless directories
.serverless/
.serverless

# AWS credentials and keys
*.pem
credentials.csv
aws-keys.json

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# macOS
.DS_Store

# Windows
Thumbs.db
ehthumbs.db

# Linux
*~

# Temporary files
*.swp
*.swo


node_modules/
dist/
.env
*.log


*.mdc
CLAUDE.md
task-rules.mdc.cursor/rules/task-rules.mdc



# Frontend ------------------------------------------------------------
# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

*.mdc
.serena/
TASKS.md
.cursor/
CLAUDE.md
.claude/ 